# Sunset View Hotel - Local APK Build Setup Script for Windows
# This script automates the setup and building of APK locally without EAS Build

param(
    [switch]$SkipPrerequisites,
    [switch]$BuildOnly,
    [string]$BuildType = "debug"  # debug, release
)

# Colors for output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Step($message) {
    Write-ColorOutput $Blue "🔄 $message"
}

function Write-Success($message) {
    Write-ColorOutput $Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput $Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput $Red "❌ $message"
}

function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-AndroidSDK {
    $androidHome = $env:ANDROID_HOME
    if (-not $androidHome) {
        return $false
    }
    return Test-Path "$androidHome\platform-tools\adb.exe"
}

function Test-JavaJDK {
    try {
        $javaVersion = java -version 2>&1
        if ($javaVersion -match "17\.|11\.") {
            return $true
        }
        return $false
    }
    catch {
        return $false
    }
}

function Install-Prerequisites {
    Write-Step "Checking prerequisites..."
    
    # Check Node.js
    if (-not (Test-Command "node")) {
        Write-Error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    }
    
    $nodeVersion = node --version
    Write-Success "Node.js version: $nodeVersion"
    
    # Check npm/yarn
    if (Test-Command "yarn") {
        $packageManager = "yarn"
        Write-Success "Using Yarn as package manager"
    } elseif (Test-Command "npm") {
        $packageManager = "npm"
        Write-Success "Using npm as package manager"
    } else {
        Write-Error "No package manager found"
        exit 1
    }
    
    # Check Java JDK
    if (-not (Test-JavaJDK)) {
        Write-Warning "Java JDK 11 or 17 not found. Please install from:"
        Write-Warning "https://adoptium.net/temurin/releases/"
        Write-Warning "Make sure to set JAVA_HOME environment variable"
        
        $continue = Read-Host "Continue anyway? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            exit 1
        }
    } else {
        Write-Success "Java JDK found"
    }
    
    # Check Android SDK
    if (-not (Test-AndroidSDK)) {
        Write-Warning "Android SDK not found. Please install Android Studio and set ANDROID_HOME"
        Write-Warning "Download from: https://developer.android.com/studio"
        
        $continue = Read-Host "Continue anyway? (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            exit 1
        }
    } else {
        Write-Success "Android SDK found at: $env:ANDROID_HOME"
    }
    
    # Install/Update Expo CLI
    Write-Step "Installing/Updating Expo CLI..."
    if ($packageManager -eq "yarn") {
        yarn global add @expo/cli
    } else {
        npm install -g @expo/cli
    }
    
    Write-Success "Prerequisites check completed"
    return $packageManager
}

function Install-Dependencies($packageManager) {
    Write-Step "Installing project dependencies..."
    
    if ($packageManager -eq "yarn") {
        yarn install
    } else {
        npm install
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install dependencies"
        exit 1
    }
    
    Write-Success "Dependencies installed successfully"
}

function Setup-AndroidEnvironment {
    Write-Step "Setting up Android build environment..."
    
    # Generate native Android code
    Write-Step "Generating native Android code..."
    npx expo prebuild --platform android --clean
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to prebuild Android project"
        exit 1
    }
    
    Write-Success "Android environment setup completed"
}

function Build-APK($buildType) {
    Write-Step "Building APK ($buildType)..."
    
    # Navigate to android directory
    if (-not (Test-Path "android")) {
        Write-Error "Android directory not found. Run prebuild first."
        exit 1
    }
    
    Set-Location "android"
    
    try {
        # Build APK based on type
        if ($buildType -eq "release") {
            Write-Step "Building release APK..."
            .\gradlew assembleRelease
            $apkPath = "app\build\outputs\apk\release\app-release-unsigned.apk"
        } else {
            Write-Step "Building debug APK..."
            .\gradlew assembleDebug
            $apkPath = "app\build\outputs\apk\debug\app-debug.apk"
        }
        
        if ($LASTEXITCODE -ne 0) {
            throw "Gradle build failed"
        }
        
        # Check if APK was created
        if (Test-Path $apkPath) {
            $fullApkPath = Resolve-Path $apkPath
            Write-Success "APK built successfully!"
            Write-Success "Location: $fullApkPath"
            
            # Get APK size
            $apkSize = [math]::Round((Get-Item $apkPath).Length / 1MB, 2)
            Write-Success "APK Size: $apkSize MB"
            
            # Copy APK to project root for easy access
            $rootApkName = "sunset-view-hotel-$buildType.apk"
            Copy-Item $apkPath "..\$rootApkName" -Force
            Write-Success "APK copied to project root as: $rootApkName"
            
        } else {
            Write-Error "APK file not found at expected location: $apkPath"
            exit 1
        }
        
    } catch {
        Write-Error "Build failed: $_"
        exit 1
    } finally {
        Set-Location ".."
    }
}

function Show-BuildInfo {
    Write-Step "Build Information:"
    Write-Output "Project: Sunset View Hotel Reservation App"
    Write-Output "Platform: Android"
    Write-Output "Build Type: $BuildType"
    Write-Output "Expo SDK: 53"
    Write-Output "React Native: 0.74.0"
    Write-Output ""
}

function Main {
    Write-ColorOutput $Blue "🏨 Sunset View Hotel - Local APK Build Script"
    Write-Output "=============================================="
    Write-Output ""
    
    Show-BuildInfo
    
    if (-not $BuildOnly) {
        if (-not $SkipPrerequisites) {
            $packageManager = Install-Prerequisites
            Install-Dependencies $packageManager
        }
        Setup-AndroidEnvironment
    }
    
    Build-APK $BuildType
    
    Write-Output ""
    Write-Success "🎉 Build process completed successfully!"
    Write-Output ""
    Write-Output "Next steps:"
    Write-Output "1. Install the APK on your Android device"
    Write-Output "2. Test the app functionality"
    Write-Output "3. For release builds, sign the APK before distribution"
    Write-Output ""
}

# Run the main function
Main
